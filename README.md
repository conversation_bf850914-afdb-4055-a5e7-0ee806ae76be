# JavaFX 桌面应用程序

这是一个简单的JavaFX桌面应用程序示例，展示了基本的用户界面组件和事件处理。

## 功能特性

- 简洁的用户界面
- 文本输入框
- 按钮交互
- 实时结果显示
- 清除功能

## 系统要求

- Java 17 或更高版本
- Maven 3.6 或更高版本

## 如何运行

### 方法1: 使用Maven运行
```bash
mvn clean javafx:run
```

### 方法2: 编译后运行
```bash
# 编译项目
mvn clean compile

# 运行应用程序
mvn javafx:run
```

### 方法3: 打包成JAR文件
```bash
# 编译并打包
mvn clean package

# 运行JAR文件（需要JavaFX运行时）
java --module-path /path/to/javafx/lib --add-modules javafx.controls,javafx.fxml -cp target/classes com.example.App
```

### 方法4: 打包成原生可执行文件（推荐）
```bash
# 编译项目
mvn clean compile

# 创建运行时镜像
mvn javafx:jlink

# 打包成原生应用程序
mvn jpackage:jpackage

# 直接运行生成的exe文件
target\dist\JavaFXApp\JavaFXApp.exe
```

## 打包和分发

### 原生可执行文件打包

本项目支持打包成Windows原生可执行文件，无需目标机器安装Java运行时。

#### 打包步骤：

1. **编译项目**
   ```bash
   mvn clean compile
   ```

2. **创建运行时镜像**
   ```bash
   mvn javafx:jlink
   ```
   这将在 `target/image` 目录创建包含JavaFX模块的自定义Java运行时。

3. **打包成原生应用程序**
   ```bash
   mvn jpackage:jpackage
   ```
   这将在 `target/dist/JavaFXApp` 目录创建完整的应用程序包。

#### 生成的文件：

- **可执行文件**: `target/dist/JavaFXApp/JavaFXApp.exe`
- **应用程序目录**: `target/dist/JavaFXApp/` （包含所有运行时文件）

#### 分发说明：

- 可以直接运行 `JavaFXApp.exe`
- 整个 `JavaFXApp` 文件夹可以复制到其他Windows机器上运行
- 不需要目标机器安装Java或JavaFX
- 应用程序包含了自定义的Java运行时环境

### 创建Windows安装程序（可选）

要创建Windows安装程序（.exe安装包），需要额外安装WiX工具集：

1. 从 [WiX官网](https://wixtoolset.org) 下载并安装WiX 3.0或更高版本
2. 将WiX添加到系统PATH环境变量
3. 运行以下命令创建安装程序：
   ```bash
   jpackage --type exe --app-image target/dist/JavaFXApp --name JavaFXApp --app-version 1.0.0 --vendor "Example Company" --dest target/installer --win-dir-chooser --win-menu --win-shortcut
   ```

## 项目结构

```
javafx-desktop-app/
├── pom.xml                           # Maven配置文件
├── README.md                         # 项目说明文档
└── src/
    └── main/
        └── java/
            ├── module-info.java      # Java模块描述文件
            └── com/
                └── example/
                    └── App.java      # 主应用程序类
```

## 应用程序界面

应用程序包含以下组件：
- 标题标签
- 文本输入框
- "点击我" 按钮
- "清除" 按钮
- 结果显示标签

## 开发说明

这个应用程序使用了以下JavaFX组件：
- `VBox` 和 `HBox` 用于布局
- `Label` 用于显示文本
- `TextField` 用于文本输入
- `Button` 用于用户交互
- CSS样式用于美化界面

## 扩展建议

你可以在此基础上添加更多功能：
- 菜单栏
- 文件操作
- 数据库连接
- 网络功能
- 更复杂的界面布局
