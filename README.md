# JavaFX 桌面应用程序

这是一个简单的JavaFX桌面应用程序示例，展示了基本的用户界面组件和事件处理。

## 功能特性

- 简洁的用户界面
- 文本输入框
- 按钮交互
- 实时结果显示
- 清除功能

## 系统要求

- Java 17 或更高版本
- Maven 3.6 或更高版本

## 如何运行

### 方法1: 使用Maven运行
```bash
mvn clean javafx:run
```

### 方法2: 编译后运行
```bash
# 编译项目
mvn clean compile

# 运行应用程序
mvn javafx:run
```

### 方法3: 打包成JAR文件
```bash
# 编译并打包
mvn clean package

# 运行JAR文件（需要JavaFX运行时）
java --module-path /path/to/javafx/lib --add-modules javafx.controls,javafx.fxml -cp target/classes com.example.App
```

## 项目结构

```
javafx-desktop-app/
├── pom.xml                           # Maven配置文件
├── README.md                         # 项目说明文档
└── src/
    └── main/
        └── java/
            ├── module-info.java      # Java模块描述文件
            └── com/
                └── example/
                    └── App.java      # 主应用程序类
```

## 应用程序界面

应用程序包含以下组件：
- 标题标签
- 文本输入框
- "点击我" 按钮
- "清除" 按钮
- 结果显示标签

## 开发说明

这个应用程序使用了以下JavaFX组件：
- `VBox` 和 `HBox` 用于布局
- `Label` 用于显示文本
- `TextField` 用于文本输入
- `Button` 用于用户交互
- CSS样式用于美化界面

## 扩展建议

你可以在此基础上添加更多功能：
- 菜单栏
- 文件操作
- 数据库连接
- 网络功能
- 更复杂的界面布局
