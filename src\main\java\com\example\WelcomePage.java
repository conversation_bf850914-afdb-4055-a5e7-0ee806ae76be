package com.example;

import javafx.animation.*;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.paint.LinearGradient;
import javafx.scene.paint.Stop;
import javafx.scene.shape.Circle;
import javafx.scene.shape.Polygon;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.util.Duration;

/**
 * 欢迎页面类，包含logo和欢迎信息
 */
public class WelcomePage extends VBox {
    
    private Runnable onEnterCallback;
    
    public WelcomePage() {
        initializeUI();
        setupAnimations();
    }
    
    private void initializeUI() {
        // 设置页面基本属性
        setAlignment(Pos.CENTER);
        setSpacing(30);
        setPadding(new Insets(50));
        
        // 设置背景渐变
        setBackground(createGradientBackground());
        
        // 创建logo
        VBox logoContainer = createLogo();
        
        // 创建欢迎标题
        Label welcomeTitle = new Label("欢迎使用");
        welcomeTitle.setFont(Font.font("Microsoft YaHei", FontWeight.BOLD, 36));
        welcomeTitle.setTextFill(Color.WHITE);
        
        // 创建应用名称
        Label appName = new Label("xm 桌面应用");
        appName.setFont(Font.font("Microsoft YaHei", FontWeight.NORMAL, 24));
        appName.setTextFill(Color.LIGHTGRAY);
        
        // 创建版本信息
        Label version = new Label("版本 1.0.0");
        version.setFont(Font.font("Microsoft YaHei", FontWeight.LIGHT, 14));
        version.setTextFill(Color.LIGHTGRAY);
        
        // 创建进入按钮
        Button enterButton = createEnterButton();
        
        // 添加所有组件
        getChildren().addAll(logoContainer, welcomeTitle, appName, version, enterButton);
    }
    
    private Background createGradientBackground() {
        LinearGradient gradient = new LinearGradient(
            0, 0, 1, 1, true, null,
            new Stop(0, Color.web("#2c3e50")),
            new Stop(1, Color.web("#3498db"))
        );
        return new Background(new BackgroundFill(gradient, null, null));
    }
    
    private VBox createLogo() {
        VBox logoContainer = new VBox();
        logoContainer.setAlignment(Pos.CENTER);
        logoContainer.setSpacing(10);
        
        // 创建logo图形
        StackPane logoGraphic = new StackPane();
        
        // 外圆
        Circle outerCircle = new Circle(50);
        outerCircle.setFill(LinearGradient.valueOf("linear-gradient(to bottom right, #e74c3c, #c0392b)"));
        outerCircle.setStroke(Color.WHITE);
        outerCircle.setStrokeWidth(3);
        
        // 内圆
        Circle innerCircle = new Circle(30);
        innerCircle.setFill(LinearGradient.valueOf("linear-gradient(to bottom right, #f39c12, #e67e22)"));
        
        // 装饰星星
        Polygon star = new Polygon();
        star.getPoints().addAll(new Double[]{
            0.0, -15.0,
            4.0, -5.0,
            15.0, -5.0,
            6.0, 2.0,
            10.0, 12.0,
            0.0, 6.0,
            -10.0, 12.0,
            -6.0, 2.0,
            -15.0, -5.0,
            -4.0, -5.0
        });
        star.setFill(Color.WHITE);
        
        logoGraphic.getChildren().addAll(outerCircle, innerCircle, star);
        logoContainer.getChildren().add(logoGraphic);
        
        return logoContainer;
    }
    
    private Button createEnterButton() {
        Button enterButton = new Button("开始使用");
        enterButton.setFont(Font.font("Microsoft YaHei", FontWeight.BOLD, 16));
        enterButton.setPrefSize(150, 45);
        
        // 设置按钮样式
        enterButton.setStyle(
            "-fx-background-color: linear-gradient(to bottom, #27ae60, #2ecc71);" +
            "-fx-text-fill: white;" +
            "-fx-background-radius: 25;" +
            "-fx-border-radius: 25;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 10, 0, 0, 3);"
        );
        
        // 添加悬停效果
        enterButton.setOnMouseEntered(e -> {
            enterButton.setStyle(
                "-fx-background-color: linear-gradient(to bottom, #2ecc71, #27ae60);" +
                "-fx-text-fill: white;" +
                "-fx-background-radius: 25;" +
                "-fx-border-radius: 25;" +
                "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.4), 15, 0, 0, 5);" +
                "-fx-scale-x: 1.05;" +
                "-fx-scale-y: 1.05;"
            );
        });
        
        enterButton.setOnMouseExited(e -> {
            enterButton.setStyle(
                "-fx-background-color: linear-gradient(to bottom, #27ae60, #2ecc71);" +
                "-fx-text-fill: white;" +
                "-fx-background-radius: 25;" +
                "-fx-border-radius: 25;" +
                "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 10, 0, 0, 3);"
            );
        });
        
        // 设置点击事件
        enterButton.setOnAction(e -> {
            if (onEnterCallback != null) {
                onEnterCallback.run();
            }
        });
        
        return enterButton;
    }
    
    private void setupAnimations() {
        // 设置初始透明度
        setOpacity(0);
        
        // 创建淡入动画
        FadeTransition fadeIn = new FadeTransition(Duration.seconds(1.5), this);
        fadeIn.setFromValue(0);
        fadeIn.setToValue(1);
        
        // 创建缩放动画
        ScaleTransition scaleIn = new ScaleTransition(Duration.seconds(1), this);
        scaleIn.setFromX(0.8);
        scaleIn.setFromY(0.8);
        scaleIn.setToX(1.0);
        scaleIn.setToY(1.0);
        
        // 并行播放动画
        ParallelTransition parallelTransition = new ParallelTransition(fadeIn, scaleIn);
        parallelTransition.play();
        
        // 为logo添加旋转动画
        StackPane logoGraphic = (StackPane) ((VBox) getChildren().get(0)).getChildren().get(0);
        RotateTransition logoRotate = new RotateTransition(Duration.seconds(2), logoGraphic);
        logoRotate.setByAngle(360);
        logoRotate.setCycleCount(1);
        logoRotate.setDelay(Duration.seconds(0.5));
        logoRotate.play();
    }
    
    public void setOnEnterCallback(Runnable callback) {
        this.onEnterCallback = callback;
    }
}
