package com.example;

import javafx.animation.*;
import javafx.application.Application;
import javafx.scene.Scene;
import javafx.scene.layout.StackPane;
import javafx.stage.Stage;
import javafx.util.Duration;

/**
 * 现代化的JavaFX桌面应用程序
 * 包含欢迎页面和主功能页面，带有平滑的页面切换动画
 */
public class App extends Application {

    private StackPane root;
    private WelcomePage welcomePage;
    private MainPage mainPage;
    private Scene scene;

    @Override
    public void start(Stage primaryStage) {
        // 设置窗口标题和图标
        primaryStage.setTitle("xm 现代桌面应用");

        // 创建根容器
        root = new StackPane();

        // 创建欢迎页面
        welcomePage = new WelcomePage();
        welcomePage.setOnEnterCallback(this::showMainPage);

        // 创建主页面（初始时隐藏）
        mainPage = new MainPage();
        mainPage.setVisible(false);

        // 添加页面到根容器
        root.getChildren().addAll(mainPage, welcomePage);

        // 创建场景
        scene = new Scene(root, 800, 600);

        // 设置场景到舞台
        primaryStage.setScene(scene);

        // 设置窗口属性
        primaryStage.setResizable(true);
        primaryStage.setMinWidth(600);
        primaryStage.setMinHeight(500);

        // 居中显示窗口
        primaryStage.centerOnScreen();

        // 显示窗口
        primaryStage.show();
    }

    /**
     * 显示主页面的方法，包含页面切换动画
     */
    private void showMainPage() {
        // 创建欢迎页面的淡出动画
        FadeTransition fadeOutWelcome = new FadeTransition(Duration.seconds(0.5), welcomePage);
        fadeOutWelcome.setFromValue(1.0);
        fadeOutWelcome.setToValue(0.0);

        // 创建欢迎页面的缩放动画
        ScaleTransition scaleOutWelcome = new ScaleTransition(Duration.seconds(0.5), welcomePage);
        scaleOutWelcome.setFromX(1.0);
        scaleOutWelcome.setFromY(1.0);
        scaleOutWelcome.setToX(0.8);
        scaleOutWelcome.setToY(0.8);

        // 并行播放欢迎页面退出动画
        ParallelTransition welcomeExit = new ParallelTransition(fadeOutWelcome, scaleOutWelcome);

        // 动画完成后的处理
        welcomeExit.setOnFinished(e -> {
            // 隐藏欢迎页面，显示主页面
            welcomePage.setVisible(false);
            mainPage.setVisible(true);

            // 创建主页面的进入动画
            FadeTransition fadeInMain = new FadeTransition(Duration.seconds(0.8), mainPage);
            fadeInMain.setFromValue(0.0);
            fadeInMain.setToValue(1.0);

            TranslateTransition slideInMain = new TranslateTransition(Duration.seconds(0.8), mainPage);
            slideInMain.setFromX(100);
            slideInMain.setToX(0);

            // 并行播放主页面进入动画
            ParallelTransition mainEnter = new ParallelTransition(fadeInMain, slideInMain);
            mainEnter.play();
        });

        // 开始播放欢迎页面退出动画
        welcomeExit.play();
    }

    public static void main(String[] args) {
        launch(args);
    }
}
