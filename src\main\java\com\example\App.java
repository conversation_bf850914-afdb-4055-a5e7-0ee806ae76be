package com.example;

import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Stage;

/**
 * 简单的JavaFX桌面应用程序
 */
public class App extends Application {

    @Override
    public void start(Stage primaryStage) {
        // 设置窗口标题
        primaryStage.setTitle("我的JavaFX桌面应用");

        // 创建主布局
        VBox root = new VBox(10);
        root.setPadding(new Insets(20));
        root.setAlignment(Pos.CENTER);

        // 创建标题标签
        Label titleLabel = new Label("欢迎使用JavaFX桌面应用！");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // 创建输入框
        TextField textField = new TextField();
        textField.setPromptText("请输入一些文字...");
        textField.setPrefWidth(300);

        // 创建按钮
        Button clickButton = new Button("点击我");
        clickButton.setPrefWidth(150);
        clickButton.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-size: 14px;");

        // 创建结果显示标签
        Label resultLabel = new Label("");
        resultLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: #27ae60;");

        // 按钮点击事件
        clickButton.setOnAction(e -> {
            String inputText = textField.getText();
            if (inputText.isEmpty()) {
                resultLabel.setText("请先输入一些文字！");
                resultLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: #e74c3c;");
            } else {
                resultLabel.setText("你输入的是: " + inputText);
                resultLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: #27ae60;");
            }
        });

        // 创建清除按钮
        Button clearButton = new Button("清除");
        clearButton.setPrefWidth(150);
        clearButton.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white; -fx-font-size: 14px;");
        
        clearButton.setOnAction(e -> {
            textField.clear();
            resultLabel.setText("");
        });

        // 创建按钮容器
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.getChildren().addAll(clickButton, clearButton);

        // 添加所有组件到主布局
        root.getChildren().addAll(titleLabel, textField, buttonBox, resultLabel);

        // 创建场景
        Scene scene = new Scene(root, 400, 300);
        
        // 设置场景到舞台
        primaryStage.setScene(scene);
        
        // 设置窗口大小不可调整（可选）
        primaryStage.setResizable(true);
        
        // 显示窗口
        primaryStage.show();
    }

    public static void main(String[] args) {
        launch(args);
    }
}
