package com.example;

import javafx.animation.*;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.effect.DropShadow;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.paint.LinearGradient;
import javafx.scene.paint.Stop;
import javafx.scene.shape.Rectangle;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.util.Duration;

/**
 * 主功能页面类，包含应用的核心功能
 */
public class MainPage extends BorderPane {
    
    private TextField textField;
    private Label resultLabel;
    private Button clickButton;
    private Button clearButton;
    
    public MainPage() {
        initializeUI();
        setupAnimations();
    }
    
    private void initializeUI() {
        // 设置背景
        setBackground(createGradientBackground());
        
        // 创建顶部区域
        VBox topSection = createTopSection();
        setTop(topSection);
        
        // 创建中心区域
        VBox centerSection = createCenterSection();
        setCenter(centerSection);
        
        // 创建底部区域
        HBox bottomSection = createBottomSection();
        setBottom(bottomSection);
    }
    
    private Background createGradientBackground() {
        LinearGradient gradient = new LinearGradient(
            0, 0, 1, 1, true, null,
            new Stop(0, Color.web("#f8f9fa")),
            new Stop(1, Color.web("#e9ecef"))
        );
        return new Background(new BackgroundFill(gradient, null, null));
    }
    
    private VBox createTopSection() {
        VBox topSection = new VBox(20);
        topSection.setAlignment(Pos.CENTER);
        topSection.setPadding(new Insets(30, 20, 20, 20));
        
        // 创建标题卡片
        VBox titleCard = new VBox(10);
        titleCard.setAlignment(Pos.CENTER);
        titleCard.setPadding(new Insets(25));
        titleCard.setBackground(new Background(new BackgroundFill(
            Color.WHITE, new CornerRadii(15), null)));
        titleCard.setEffect(new DropShadow(10, Color.gray(0, 0.2)));
        
        Label titleLabel = new Label("xm 桌面应用");
        titleLabel.setFont(Font.font("Microsoft YaHei", FontWeight.BOLD, 28));
        titleLabel.setTextFill(Color.web("#2c3e50"));
        
        Label subtitleLabel = new Label("体验现代化的用户界面设计");
        subtitleLabel.setFont(Font.font("Microsoft YaHei", FontWeight.NORMAL, 14));
        subtitleLabel.setTextFill(Color.web("#7f8c8d"));
        
        titleCard.getChildren().addAll(titleLabel, subtitleLabel);
        topSection.getChildren().add(titleCard);
        
        return topSection;
    }
    
    private VBox createCenterSection() {
        VBox centerSection = new VBox(25);
        centerSection.setAlignment(Pos.CENTER);
        centerSection.setPadding(new Insets(20));
        centerSection.setMaxWidth(500);
        
        // 创建输入卡片
        VBox inputCard = createInputCard();
        
        // 创建按钮区域
        HBox buttonArea = createButtonArea();
        
        // 创建结果卡片
        VBox resultCard = createResultCard();
        
        centerSection.getChildren().addAll(inputCard, buttonArea, resultCard);
        return centerSection;
    }
    
    private VBox createInputCard() {
        VBox inputCard = new VBox(15);
        inputCard.setAlignment(Pos.CENTER);
        inputCard.setPadding(new Insets(25));
        inputCard.setBackground(new Background(new BackgroundFill(
            Color.WHITE, new CornerRadii(15), null)));
        inputCard.setEffect(new DropShadow(8, Color.gray(0, 0.15)));
        
        Label inputLabel = new Label("请输入您的文本");
        inputLabel.setFont(Font.font("Microsoft YaHei", FontWeight.BOLD, 16));
        inputLabel.setTextFill(Color.web("#34495e"));
        
        textField = new TextField();
        textField.setPromptText("在这里输入一些文字...");
        textField.setPrefHeight(45);
        textField.setFont(Font.font("Microsoft YaHei", 14));
        textField.setStyle(
            "-fx-background-color: #f8f9fa;" +
            "-fx-border-color: #dee2e6;" +
            "-fx-border-radius: 8;" +
            "-fx-background-radius: 8;" +
            "-fx-padding: 10;" +
            "-fx-border-width: 2;"
        );
        
        // 添加焦点效果
        textField.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal) {
                textField.setStyle(
                    "-fx-background-color: white;" +
                    "-fx-border-color: #3498db;" +
                    "-fx-border-radius: 8;" +
                    "-fx-background-radius: 8;" +
                    "-fx-padding: 10;" +
                    "-fx-border-width: 2;"
                );
            } else {
                textField.setStyle(
                    "-fx-background-color: #f8f9fa;" +
                    "-fx-border-color: #dee2e6;" +
                    "-fx-border-radius: 8;" +
                    "-fx-background-radius: 8;" +
                    "-fx-padding: 10;" +
                    "-fx-border-width: 2;"
                );
            }
        });
        
        inputCard.getChildren().addAll(inputLabel, textField);
        return inputCard;
    }
    
    private HBox createButtonArea() {
        HBox buttonArea = new HBox(20);
        buttonArea.setAlignment(Pos.CENTER);
        
        clickButton = createStyledButton("处理文本", "#3498db", "#2980b9");
        clearButton = createStyledButton("清除内容", "#e74c3c", "#c0392b");
        
        // 设置按钮事件
        clickButton.setOnAction(e -> handleTextProcessing());
        clearButton.setOnAction(e -> handleClearAction());
        
        buttonArea.getChildren().addAll(clickButton, clearButton);
        return buttonArea;
    }
    
    private Button createStyledButton(String text, String color1, String color2) {
        Button button = new Button(text);
        button.setFont(Font.font("Microsoft YaHei", FontWeight.BOLD, 14));
        button.setPrefSize(140, 45);
        
        String baseStyle = String.format(
            "-fx-background-color: linear-gradient(to bottom, %s, %s);" +
            "-fx-text-fill: white;" +
            "-fx-background-radius: 25;" +
            "-fx-border-radius: 25;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 2);",
            color1, color2
        );
        
        String hoverStyle = String.format(
            "-fx-background-color: linear-gradient(to bottom, %s, %s);" +
            "-fx-text-fill: white;" +
            "-fx-background-radius: 25;" +
            "-fx-border-radius: 25;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 12, 0, 0, 4);" +
            "-fx-scale-x: 1.05;" +
            "-fx-scale-y: 1.05;",
            color2, color1
        );
        
        button.setStyle(baseStyle);
        
        button.setOnMouseEntered(e -> button.setStyle(hoverStyle));
        button.setOnMouseExited(e -> button.setStyle(baseStyle));
        
        return button;
    }
    
    private VBox createResultCard() {
        VBox resultCard = new VBox(15);
        resultCard.setAlignment(Pos.CENTER);
        resultCard.setPadding(new Insets(25));
        resultCard.setBackground(new Background(new BackgroundFill(
            Color.WHITE, new CornerRadii(15), null)));
        resultCard.setEffect(new DropShadow(8, Color.gray(0, 0.15)));
        
        Label resultTitle = new Label("处理结果");
        resultTitle.setFont(Font.font("Microsoft YaHei", FontWeight.BOLD, 16));
        resultTitle.setTextFill(Color.web("#34495e"));
        
        resultLabel = new Label("等待您的输入...");
        resultLabel.setFont(Font.font("Microsoft YaHei", 14));
        resultLabel.setTextFill(Color.web("#7f8c8d"));
        resultLabel.setWrapText(true);
        resultLabel.setAlignment(Pos.CENTER);
        resultLabel.setMinHeight(50);
        
        resultCard.getChildren().addAll(resultTitle, resultLabel);
        return resultCard;
    }
    
    private HBox createBottomSection() {
        HBox bottomSection = new HBox();
        bottomSection.setAlignment(Pos.CENTER);
        bottomSection.setPadding(new Insets(20));
        
        Label footerLabel = new Label("© 2024 JavaFX Desktop App - 现代化桌面应用程序");
        footerLabel.setFont(Font.font("Microsoft YaHei", 12));
        footerLabel.setTextFill(Color.web("#95a5a6"));
        
        bottomSection.getChildren().add(footerLabel);
        return bottomSection;
    }
    
    private void handleTextProcessing() {
        String inputText = textField.getText().trim();
        
        if (inputText.isEmpty()) {
            showResult("请先输入一些文字！", "#e74c3c");
            // 添加摇摆动画提示
            addShakeAnimation(textField);
        } else {
            showResult("您输入的内容是: " + inputText, "#27ae60");
            // 添加成功动画
            addSuccessAnimation(resultLabel);
        }
    }
    
    private void handleClearAction() {
        textField.clear();
        resultLabel.setText("等待您的输入...");
        resultLabel.setTextFill(Color.web("#7f8c8d"));
        
        // 添加清除动画
        addFadeAnimation(resultLabel);
    }
    
    private void showResult(String message, String color) {
        resultLabel.setText(message);
        resultLabel.setTextFill(Color.web(color));
    }
    
    private void setupAnimations() {
        // 设置初始状态
        setOpacity(0);
        setTranslateX(50);
        
        // 创建进入动画
        Timeline slideIn = new Timeline(
            new KeyFrame(Duration.ZERO,
                new KeyValue(opacityProperty(), 0),
                new KeyValue(translateXProperty(), 50)
            ),
            new KeyFrame(Duration.seconds(0.8),
                new KeyValue(opacityProperty(), 1),
                new KeyValue(translateXProperty(), 0)
            )
        );
        slideIn.play();
    }
    
    private void addShakeAnimation(javafx.scene.Node node) {
        TranslateTransition shake = new TranslateTransition(Duration.millis(50), node);
        shake.setFromX(0);
        shake.setToX(10);
        shake.setCycleCount(6);
        shake.setAutoReverse(true);
        shake.play();
    }
    
    private void addSuccessAnimation(javafx.scene.Node node) {
        ScaleTransition scale = new ScaleTransition(Duration.millis(200), node);
        scale.setFromX(1.0);
        scale.setFromY(1.0);
        scale.setToX(1.1);
        scale.setToY(1.1);
        scale.setCycleCount(2);
        scale.setAutoReverse(true);
        scale.play();
    }
    
    private void addFadeAnimation(javafx.scene.Node node) {
        FadeTransition fade = new FadeTransition(Duration.millis(300), node);
        fade.setFromValue(1.0);
        fade.setToValue(0.3);
        fade.setCycleCount(2);
        fade.setAutoReverse(true);
        fade.play();
    }
}
