# 应用程序图标设置指南

## 当前状态
✅ **程序名称已更改**: "XM桌面应用"
✅ **供应商信息已更新**: "XM Company"
✅ **生成的exe文件**: `target/dist/XM桌面应用/XM桌面应用.exe`

## 如何添加自定义图标

### 方法1: 使用在线图标生成器（推荐）

1. **访问在线图标生成器**:
   - https://www.favicon-generator.org/
   - https://convertio.co/png-ico/
   - https://icoconvert.com/

2. **准备图标素材**:
   - 创建一个256x256像素的PNG图片
   - 建议使用简洁的设计，避免过于复杂的细节
   - 确保在小尺寸下仍然清晰可见

3. **生成ICO文件**:
   - 上传您的PNG图片
   - 选择生成多种尺寸 (16x16, 32x32, 48x48, 64x64, 128x128, 256x256)
   - 下载生成的ICO文件

4. **放置图标文件**:
   ```
   src/main/resources/images/app-icon.ico
   ```

### 方法2: 使用图像编辑软件

1. **使用GIMP (免费)**:
   - 下载并安装GIMP
   - 创建256x256的新图像
   - 设计您的图标
   - 导出为ICO格式

2. **使用Photoshop**:
   - 需要安装ICO插件
   - 创建图标设计
   - 保存为ICO格式

### 方法3: 使用Python脚本（需要安装依赖）

如果您有Python环境，可以安装PIL库并使用脚本生成图标：

```bash
pip install Pillow
```

然后运行我们之前创建的Python脚本。

## 配置pom.xml

在pom.xml中添加图标配置：

```xml
<plugin>
    <groupId>org.panteleyev</groupId>
    <artifactId>jpackage-maven-plugin</artifactId>
    <version>1.6.0</version>
    <configuration>
        <name>XM桌面应用</name>
        <appVersion>${project.version}</appVersion>
        <vendor>XM Company</vendor>
        <destination>target/dist</destination>
        <module>javafx.desktop.app/com.example.App</module>
        <runtimeImage>target/image</runtimeImage>
        <type>APP_IMAGE</type>
        <icon>src/main/resources/images/app-icon.ico</icon>  <!-- 添加这行 -->
        <javaOptions>
            <option>-Dfile.encoding=UTF-8</option>
        </javaOptions>
    </configuration>
</plugin>
```

## 重新打包

添加图标后，重新运行打包命令：

```bash
mvn clean compile
mvn javafx:jlink
mvn jpackage:jpackage
```

## 图标设计建议

1. **尺寸**: 256x256像素（会自动生成其他尺寸）
2. **格式**: ICO格式（Windows）或PNG格式
3. **设计**: 简洁明了，避免过多细节
4. **颜色**: 使用对比鲜明的颜色
5. **背景**: 透明背景效果更好

## 当前配置总结

- **应用名称**: XM桌面应用
- **供应商**: XM Company
- **版本**: 1.0.0
- **生成位置**: `target/dist/XM桌面应用/`
- **可执行文件**: `XM桌面应用.exe`

## 注意事项

- 图标文件必须是ICO格式（Windows）
- 确保图标文件路径正确
- 重新打包后图标才会生效
- 如果图标不显示，检查文件路径和格式
